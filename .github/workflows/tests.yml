name: Go Tests

on:
  push:
    branches:
      - main
    paths-ignore:
      - "README.md"
      - "LICENSE"
  pull_request:
    branches:
      - main

jobs:
  test:
    name: Run Go Tests
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pull-requests: write
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - name: Set up Go
        uses: actions/setup-go@v5
        with:
          go-version: "^1.24"
          check-latest: true

      - name: Get dependencies
        run: go mod tidy

      - name: Run tests
        run: go test -v -race ./...
        # run: go test -v -race -coverprofile=coverage.out ./...

      # - name: Convert coverage to badge
      #   run: |
      #     total_coverage=$(go tool cover -func=coverage.out | grep total | grep -Eo '[0-9]+\.[0-9]+')
      #     rounded_coverage=$(printf "%.0f" $total_coverage)
      #     if [ $rounded_coverage -lt 50 ]; then
      #       color="red"
      #     elif [ $rounded_coverage -lt 80 ]; then
      #       color="yellow"
      #     else
      #       color="brightgreen"
      #     fi
      #     echo "COVERAGE=$rounded_coverage" >> $GITHUB_ENV
      #     echo "COLOR=$color" >> $GITHUB_ENV

      # - name: Update README badge
      #   run: |
      #     sed -i "s|https://img.shields.io/badge/Coverage-.*-.*\.svg|https://img.shields.io/badge/Coverage-${{ env.COVERAGE }}%25-${{ env.COLOR }}.svg|" README.md

      # - name: Commit changes
      #   if: github.ref == 'refs/heads/main'
      #   run: |
      #     git config --local user.email "github-actions[bot]@users.noreply.github.com"
      #     git config --local user.name "github-actions[bot]"
      #     git add README.md
      #     git commit -m "docs: Updated coverage badge." || echo "No changes to commit"
      #     git push || echo "No changes to push"