name: Deploy content-maestro

on:
  workflow_dispatch:
  workflow_run:
    workflows: ["Release content-maestro"]
    types:
      - completed
    branches:
      - main

permissions:
  contents: read

jobs:
  deploy:
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Get repository name
        id: repo_name
        run: echo "repo=$(basename ${{ github.repository }})" >> $GITHUB_OUTPUT

      - name: Execute remote commands via SSH
        uses: appleboy/ssh-action@v1.1.0
        with:
          host: ${{ secrets.SSH_HOST }}
          username: ${{ secrets.SSH_USER }}
          password: ${{ secrets.SSH_PASSWORD }}
          script: |
            set -e  # Exit on any command error

            if ! systemctl is-active --quiet docker; then
              echo "Docker is not running. Starting Docker..."
              sudo systemctl start docker
            fi

            if [ ! -e /var/run/docker.sock ]; then
              echo "Docker socket does not exist at /var/run/docker.sock"
              exit 1
            fi

            if [ ! -w /var/run/docker.sock ]; then
              echo "Current user does not have write access to Docker socket."
              sudo chmod 666 /var/run/docker.sock
            fi

            if ! groups $USER | grep -q docker; then
              echo "Adding $USER to docker group"
              sudo usermod -aG docker $USER
              newgrp docker
            fi

            echo "Docker setup complete. Proceeding with deployment."

            REPO_NAME="${{ github.repository }}"
            REPO_FOLDER="$HOME/apps/${REPO_NAME##*/}"

            echo "Deploying $REPO_NAME to $REPO_FOLDER"

            if ! command -v docker >/dev/null 2>&1; then
              echo "Installing Docker..."
              sudo apt-get update
              sudo apt-get install -y docker.io
            fi

            if [ -d "$REPO_FOLDER" ]; then
              echo "Removing existing repository folder..."
              rm -rf "$REPO_FOLDER"
            fi

            echo "Cloning new version of the repository..."
            git clone "https://github.com/${{ github.repository }}.git" "$REPO_FOLDER"

            cd "$REPO_FOLDER"

            echo "Setting up .env file..."
            echo "${{ secrets.ENV_PROD }}" > .env

            LATEST_TAG=$(curl -s https://api.github.com/repos/${{ github.repository }}/tags | grep -o '"name": "[^"]*' | head -1 | cut -d'"' -f4)
            APP_VERSION=${LATEST_TAG:-dev}
            echo "Latest tag: $APP_VERSION"

            echo "Building Docker image with version $APP_VERSION"
            export APP_VERSION
            docker compose build --build-arg APP_VERSION=$APP_VERSION

            if docker inspect content-maestro >/dev/null 2>&1; then
              echo "Stopping and removing existing container..."
              docker compose down --rmi all
            fi

            docker compose up -d

            echo "Deployment complete!"