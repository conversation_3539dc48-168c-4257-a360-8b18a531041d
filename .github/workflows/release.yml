name: Release content-maestro

on:
  workflow_dispatch:
  workflow_run:
    workflows: ["Go Tests"]
    types:
      - completed
    branches:
      - main

permissions:
  contents: write
  issues: write
  pull-requests: write

jobs:
  release:
    name: Release
    runs-on: ubuntu-latest
    if: ${{ github.event.workflow_run.conclusion == 'success' || github.event_name == 'workflow_dispatch' }}
    steps:
      - name: Checkout
        uses: actions/checkout@v3
        with:
          fetch-depth: 0
          persist-credentials: false

      - name: Configure Git
        run: |
          git config --global user.name 'github-actions'
          git config --global user.email '<EMAIL>'

      - name: Setup Node.js
        uses: actions/setup-node@v3
        with:
          node-version: "lts/*"

      - name: Install dependencies
        run: npm install

      - name: Release
        env:
          GH_TOKEN: ${{ secrets.GH_TOKEN }}
          GITHUB_TOKEN: ${{ secrets.GH_TOKEN }}
          NODE_AUTH_TOKEN: ${{ secrets.GH_TOKEN }}
        run: npx semantic-release --debug
