services:
  content-maestro:
    container_name: content-maestro
    restart: always
    image: content-maestro:latest
    build:
      context: .
      dockerfile: Dockerfile
      args:
        APP_VERSION: ${APP_VERSION}
    env_file:
      - .env
    environment:
      - TZ=Europe/Kiev
      - APP_VERSION=${APP_VERSION}
    ports:
      - "${API_PORT:-8080}:${API_PORT:-8080}"
    volumes:
      - content-maestro-data:/app/data/badger
      - content-maestro-tmp:/app/tmp/gh_project_img
    networks:
      - think-root-network

volumes:
  content-maestro-data:
    name: content-maestro-data
  content-maestro-tmp:
    name: content-maestro-tmp

networks:
  think-root-network:
    external: true